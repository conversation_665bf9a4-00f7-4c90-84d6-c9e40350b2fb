%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &292466971717829756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7980485743538428503}
  m_Layer: 0
  m_Name: Bone.009_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7980485743538428503
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 292466971717829756}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.08181919, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5276609775834701150}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &871770539207879257
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 617731836997710821}
  m_Layer: 0
  m_Name: Bone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &617731836997710821
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871770539207879257}
  serializedVersion: 2
  m_LocalRotation: {x: -0.4990898, y: 0.5009086, z: -0.5008976, w: -0.49910071}
  m_LocalPosition: {x: 0.108260706, y: 0, z: 0.080699295}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8381748351593586542}
  - {fileID: 3780527614113320519}
  - {fileID: 2086873226925897898}
  m_Father: {fileID: 2951807290100387977}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1011966863698304532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5276609775834701150}
  m_Layer: 0
  m_Name: Bone.009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5276609775834701150
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1011966863698304532}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6052857, y: -0.06887864, z: 0.024656566, w: 0.79263926}
  m_LocalPosition: {x: 0.000000004097819, y: 0.08768789, z: 0.0000000020489095}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7980485743538428503}
  m_Father: {fileID: 3780527614113320519}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1737146766634386371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 885824687701458451}
  - component: {fileID: 6886339340038415854}
  m_Layer: 0
  m_Name: wig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &885824687701458451
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1737146766634386371}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: 0.018561121, y: 1.007162, z: 9.6026945}
  m_LocalScale: {x: 82.10711, y: 30.5343, z: 82.10711}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6072603019738628119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6886339340038415854
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1737146766634386371}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 70d7c341a3adc2f4ba3de6a9afde4390, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -2961525095540706010, guid: 78141d2de80ab0344acfd3cfb2cd2592, type: 3}
  m_Bones:
  - {fileID: 617731836997710821}
  - {fileID: 8381748351593586542}
  - {fileID: 3521603029420572557}
  - {fileID: 4188526702921341530}
  - {fileID: 825685229138959655}
  - {fileID: 9097667072449228507}
  - {fileID: 3780527614113320519}
  - {fileID: 5276609775834701150}
  - {fileID: 2086873226925897898}
  - {fileID: 9202045985668204457}
  - {fileID: 5884344956455363594}
  - {fileID: 8837634882364929429}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 617731836997710821}
  m_AABB:
    m_Center: {x: -0.027939036, y: 0.041646138, z: 0.09509024}
    m_Extent: {x: 0.076309025, y: 0.123624235, z: 0.08451295}
  m_DirtyAABB: 0
--- !u!1 &1962206775571197700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3780527614113320519}
  m_Layer: 0
  m_Name: Bone.008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3780527614113320519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962206775571197700}
  serializedVersion: 2
  m_LocalRotation: {x: 0.69438356, y: -0.0000000094977715, z: 0.13226256, w: 0.7073459}
  m_LocalPosition: {x: 0.0000000022409947, y: 0.13277254, z: 5.4539705e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5276609775834701150}
  m_Father: {fileID: 617731836997710821}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2155974411048078557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5884344956455363594}
  m_Layer: 0
  m_Name: Bone.002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5884344956455363594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2155974411048078557}
  serializedVersion: 2
  m_LocalRotation: {x: 0.1331646, y: -0.13316454, z: -0.69445467, w: 0.69445455}
  m_LocalPosition: {x: 0.108260706, y: 0, z: 0.080699295}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8837634882364929429}
  m_Father: {fileID: 2951807290100387977}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2271945006600052469
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2086873226925897898}
  m_Layer: 0
  m_Name: Bone.010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2086873226925897898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2271945006600052469}
  serializedVersion: 2
  m_LocalRotation: {x: -0.67152447, y: 0.029788738, z: 0.22004613, w: 0.7069281}
  m_LocalPosition: {x: 0.0000000022409947, y: 0.13277254, z: 5.4539705e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9202045985668204457}
  m_Father: {fileID: 617731836997710821}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2520726823382619629
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4188526702921341530}
  m_Layer: 0
  m_Name: Bone.005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4188526702921341530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2520726823382619629}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0009112381, y: -0.00000001862654, z: 0.0058819004, w: 0.9999823}
  m_LocalPosition: {x: 9.313225e-10, y: 0.026588056, z: 0}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 825685229138959655}
  m_Father: {fileID: 3521603029420572557}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3106061791761164268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7739158408940060241}
  - component: {fileID: 4108303810667807015}
  m_Layer: 0
  m_Name: Cube.001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7739158408940060241
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3106061791761164268}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: 0.018561121, y: 1.007162, z: -9.726232}
  m_LocalScale: {x: 82.10711, y: 30.5343, z: 82.10711}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6072603019738628119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &4108303810667807015
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3106061791761164268}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 70d7c341a3adc2f4ba3de6a9afde4390, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4493585093827132993, guid: 78141d2de80ab0344acfd3cfb2cd2592, type: 3}
  m_Bones:
  - {fileID: 617731836997710821}
  - {fileID: 8381748351593586542}
  - {fileID: 3521603029420572557}
  - {fileID: 4188526702921341530}
  - {fileID: 825685229138959655}
  - {fileID: 9097667072449228507}
  - {fileID: 3780527614113320519}
  - {fileID: 5276609775834701150}
  - {fileID: 2086873226925897898}
  - {fileID: 9202045985668204457}
  - {fileID: 5884344956455363594}
  - {fileID: 8837634882364929429}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 617731836997710821}
  m_AABB:
    m_Center: {x: -0.018652044, y: 0.052580748, z: -0.09382653}
    m_Extent: {x: 0.08539822, y: 0.11967456, z: 0.096775845}
  m_DirtyAABB: 0
--- !u!1 &3632477755827870403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2951807290100387977}
  m_Layer: 0
  m_Name: Armature
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2951807290100387977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3632477755827870403}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: -4.1522574, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 617731836997710821}
  - {fileID: 5884344956455363594}
  m_Father: {fileID: 6072603019738628119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3970927867522209468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 825685229138959655}
  m_Layer: 0
  m_Name: Bone.006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &825685229138959655
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3970927867522209468}
  serializedVersion: 2
  m_LocalRotation: {x: 0.14487284, y: -0.000000046865352, z: 0.9350548, w: 0.32354993}
  m_LocalPosition: {x: 0.0000000014308346, y: 0.114573754, z: 6.6902636e-11}
  m_LocalScale: {x: 1.0000002, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9097667072449228507}
  m_Father: {fileID: 4188526702921341530}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5277794164645600936
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3355272131036890214}
  m_Layer: 0
  m_Name: Bone.011_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3355272131036890214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5277794164645600936}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.14511128, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9202045985668204457}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5410094087322615718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8381748351593586542}
  m_Layer: 0
  m_Name: Bone.001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8381748351593586542
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5410094087322615718}
  serializedVersion: 2
  m_LocalRotation: {x: -0.1076151, y: 0.00018330102, z: -0.6995973, w: 0.706387}
  m_LocalPosition: {x: 0.0000000022409947, y: 0.13277254, z: 5.4539705e-16}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3521603029420572557}
  m_Father: {fileID: 617731836997710821}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5846734854016675443
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3521603029420572557}
  m_Layer: 0
  m_Name: Bone.004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3521603029420572557
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5846734854016675443}
  serializedVersion: 2
  m_LocalRotation: {x: 0.07444898, y: 0.000000055393592, z: -0.018285854, w: 0.9970572}
  m_LocalPosition: {x: 9.313225e-10, y: 0.06814494, z: -0.000000001117587}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4188526702921341530}
  m_Father: {fileID: 8381748351593586542}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5881254422405070694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9202045985668204457}
  m_Layer: 0
  m_Name: Bone.011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9202045985668204457
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5881254422405070694}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6284445, y: 0.03232127, z: 0.13930735, w: 0.7645955}
  m_LocalPosition: {x: -0.000000002235174, y: 0.085668884, z: -7.45058e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3355272131036890214}
  m_Father: {fileID: 2086873226925897898}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5955920385156714433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4068955749861803840}
  m_Layer: 0
  m_Name: Bone.003_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4068955749861803840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5955920385156714433}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.046917014, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8837634882364929429}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6178693527184630899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8837634882364929429}
  m_Layer: 0
  m_Name: Bone.003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8837634882364929429
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6178693527184630899}
  serializedVersion: 2
  m_LocalRotation: {x: 0.15787324, y: -0.00000004499986, z: 0.0000000016116499, w: 0.98745936}
  m_LocalPosition: {x: -7.105427e-16, y: 0.052980356, z: -0.0000000029802318}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4068955749861803840}
  m_Father: {fileID: 5884344956455363594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6359331939895416216
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6933184800610147186}
  - component: {fileID: 2818400935962550882}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6933184800610147186
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359331939895416216}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6072603019738628119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2818400935962550882
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359331939895416216}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 70d7c341a3adc2f4ba3de6a9afde4390, type: 2}
  - {fileID: 2100000, guid: 089df2b5e9699af40bdcaad42291d420, type: 2}
  - {fileID: 2100000, guid: 3efb6bd9355a63e419ef2954494d3af6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5495902117074765545, guid: 78141d2de80ab0344acfd3cfb2cd2592, type: 3}
  m_Bones:
  - {fileID: 617731836997710821}
  - {fileID: 8381748351593586542}
  - {fileID: 3521603029420572557}
  - {fileID: 4188526702921341530}
  - {fileID: 825685229138959655}
  - {fileID: 9097667072449228507}
  - {fileID: 3780527614113320519}
  - {fileID: 5276609775834701150}
  - {fileID: 2086873226925897898}
  - {fileID: 9202045985668204457}
  - {fileID: 5884344956455363594}
  - {fileID: 8837634882364929429}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 617731836997710821}
  m_AABB:
    m_Center: {x: 0.052958816, y: 0.079183556, z: -0.010071613}
    m_Extent: {x: 0.23694476, y: 0.22621539, z: 0.18347225}
  m_DirtyAABB: 0
--- !u!1 &6849386696393104045
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6072603019738628119}
  - component: {fileID: 9155435300649154489}
  - component: {fileID: 1319284046017279285}
  m_Layer: 0
  m_Name: DuckHat
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6072603019738628119
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6849386696393104045}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 17.044, y: 0.864, z: 0}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2951807290100387977}
  - {fileID: 6933184800610147186}
  - {fileID: 7739158408940060241}
  - {fileID: 885824687701458451}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9155435300649154489
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6849386696393104045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 892faa80548ad72469a70f1220b441c3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneObject:
    RawGuidValue: 00000000000000000000000000000000
  ItemID: 0
  _IsAvailable: 1
  sellableItemIndex: 0
  itemPrefab:
    RawGuidValue: 00000000000000000000000000000000
  hatId: 0
  hatPrefab: {fileID: 0}
--- !u!114 &1319284046017279285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6849386696393104045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1552182283, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SortKey: 547663021
  ObjectInterest: 1
  Flags: 262145
  NestedObjects: []
  NetworkedBehaviours:
  - {fileID: 9155435300649154489}
  ForceRemoteRenderTimeframe: 0
--- !u!1 &8951892409350331477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2052654681468451297}
  m_Layer: 0
  m_Name: Bone.007_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2052654681468451297
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8951892409350331477}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.08261188, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9097667072449228507}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9125336036514222433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9097667072449228507}
  m_Layer: 0
  m_Name: Bone.007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9097667072449228507
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9125336036514222433}
  serializedVersion: 2
  m_LocalRotation: {x: -0.061283488, y: 0.000000004039642, z: -0.3955431, w: 0.91640055}
  m_LocalPosition: {x: 0.000000007823109, y: 0.10648399, z: -0.000000002235174}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2052654681468451297}
  m_Father: {fileID: 825685229138959655}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
