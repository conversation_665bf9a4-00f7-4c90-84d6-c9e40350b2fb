using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Fusion;
using UnityEngine;
using UnityEngine.SceneManagement;
using DG.Tweening;

namespace SimpleFPS {

    #region Data Structures
    public enum EGameplayState {
        None = 0,
        Lobby = 1,
        ReadyToStartGame = 2,
        PreMatch = 3,
        Match = 4,
        EndMatch = 5,
        WinnerDeclare = 6,
        GameOver = 7,
        Loading = 8
    }

    public enum ESelectedGameMode {
        Sifa,
        Knife,
        P<PERSON>tki,
        Assyk,
        Lobby,
        Klassiki,
        Z<PERSON>,
        Caps,
        AltP<PERSON>tki,
        Dodge<PERSON>all,
        Bottle,
        VictoryScene
    }
    #endregion

    /// <summary>
    /// The main GameManager that controls the overall gameplay flow, states, and scene transitions.
    /// Durations (pregame, match, respawn) are now taken from the current IGameModeConfig found in the scene.
    /// </summary>
    public class GameManager : NetworkBehaviour {
        public static GameManager Instance { get; private set; }

        #region Public Fields
        [Header("References")]
        public UIGame gameUI;
        public PlayerController playerController;
        //public TicketEventController ticketEventController;

        [Header("Scenes")]
        public string VictorySceneName = "VictoryScene";
        public string LobbyAfterGameSceneName = "LobbyAfterGameScene";
        public string LobbySceneName = "LobbyScene";
        private int currentCycleIndex = 0;
        public int gameAmount = 6;

        [Header("Prepare Next Match Settings")]
        [SerializeField] private float nextMatchPrepareDuration = 5f;

        [Header("Game Mode Settings")]
        public ESelectedGameMode selectedGameMode;

        [Header("Squid Game Mode Settings")]
        [Tooltip("If true, elimination logic works normally. If false, players always spawn at regular spawn points regardless of IsAlive status.")]
        public bool IsSquidGameMode = true;
        #endregion

        #region Networked Properties
        [Networked, Capacity(32)] public NetworkDictionary<PlayerRef, PlayerData> PlayerData { get; }
        [Networked] public TickTimer RemainingTime { get; set; }
        [Networked] public EGameplayState State { get; set; }
        [Networked, Capacity(32)] public NetworkString<_32> lastWinner { get; set; }
        #endregion

        #region Local Variables
        // Used in spawn methods to avoid multiple spawn calls
        [SerializeField] private HashSet<PlayerRef> pendingSpawns = new HashSet<PlayerRef>();

        // We store the current game mode interface (Sifa, Knife, etc.)
        [SerializeField] private IGameModeConfig currentGameMode;

        // If no GameMode is found in the scene, we load the next scene after this delay.
        [SerializeField] private float loadNextSceneDuration = 5f;

        // Track disconnected players for cleanup
        private Dictionary<PlayerRef, float> disconnectedPlayers = new Dictionary<PlayerRef, float>();
        [SerializeField] private float disconnectedPlayerCleanupDelay = 10f; // Remove after 10 seconds
        #endregion


        #region Unity & Fusion Lifecycle
        public override void Spawned() {

            SceneManager.sceneLoaded += OnSceneLoaded;

            if (Instance == null) {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (Instance != this) {
                Destroy(gameObject);
                return;
            }

            if (Runner.Mode == SimulationModes.Server) {
                // any server init
            }
            if (Runner.GameMode == GameMode.Shared) {
                throw new System.NotSupportedException("Shared Mode is not supported.");
            }

            DetectCurrentSceneMode();

            // If currentGameMode is still null, retry detection periodically
            if (currentGameMode == null) {
                InvokeRepeating(nameof(RetryDetectSceneMode), 2f, 2f);
            }
        }

        private void RetryDetectSceneMode() {
            DetectCurrentSceneMode();

            // Stop retrying once we find a game mode
            if (currentGameMode != null) {
                CancelInvoke(nameof(RetryDetectSceneMode));
            }
        }

        public override void FixedUpdateNetwork() {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Automatically spawn or despawn players as needed
            PlayerManager.UpdatePlayerConnections(Runner, SpawnPlayer, DespawnPlayer);

            // Cleanup disconnected players periodically (every 5 seconds)
            if (Runner.Tick % 300 == 0) { // 300 ticks = 5 seconds at 60 FPS
                CleanupDisconnectedPlayers();
            }

            switch (State) {
                case EGameplayState.Lobby:
                    // Periodically check ready status in lobby to handle edge cases
                    if (Runner.Tick % 60 == 0) { // Check every second (60 ticks)
                        CheckAllPlayersReady();
                    }
                    break;
                case EGameplayState.ReadyToStartGame:
                    UpdateReadyToStartGame();
                    break;
                case EGameplayState.PreMatch:
                    UpdatePreMatch();
                    break;
                case EGameplayState.Match:
                    UpdateMatch();
                    break;
                case EGameplayState.EndMatch:
                    UpdateEndMatch();
                    break;
                case EGameplayState.WinnerDeclare:
                    UpdateWinnerDeclare();
                    break;
                case EGameplayState.GameOver:
                    UpdateGameOver();
                    break;
            }
        }
        public override void Render() {
            if (Runner.Mode == SimulationModes.Server) {
                return;
            }
        }
        private void OnDisable() {
            SceneManager.sceneLoaded -= OnSceneLoaded;
        }
        #endregion


        #region Match Methods
        private void UpdateReadyToStartGame() {
            if (RemainingTime.Expired(Runner)) {
                LoadNextScene();
            }
        }
        private void UpdatePreMatch() {
            if (RemainingTime.Expired(Runner)) {
                StartMatch();
            }
        }
        private void UpdateMatch() {
            if (RemainingTime.Expired(Runner)) {
                EndMatch();
            }
        }
        private void UpdateEndMatch() {
            if (RemainingTime.Expired(Runner)) {
                LoadNextScene();
            }
        }
        private void UpdateWinnerDeclare() {
            if (RemainingTime.Expired(Runner)) {
                LoadVictoryScene();
            }
        }
        private void UpdateGameOver() {
            if (RemainingTime.Expired(Runner)) {
                ResetGame();
                LoadLobbyScene();
            }
        }

        private void StartMatch() {
            if (!Object.HasStateAuthority) {
                return;
            }
            State = EGameplayState.Match;

            // Use match duration from the current mode
            if (currentGameMode != null) {
                RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.MatchDuration);
            }

            currentGameMode?.OnRoundStarted();
        }
        public void EndMatch() {
            if (!Object.HasStateAuthority) {
                return;
            }
            if (State == EGameplayState.EndMatch || State == EGameplayState.GameOver) {
                return;
            }

            currentGameMode?.OnRoundEnded();

            // Set all players as alive for the next match
            foreach (var pair in PlayerData) {
                var pd = pair.Value;
                if (pd.IsConnected) {
                    pd.IsAlive = true;
                    PlayerData.Set(pair.Key, pd);
                }
            }

            CheckToFinishGame();
        }

        #endregion


        #region Scene Management
        private void OnSceneLoaded(Scene scene, LoadSceneMode mode) {
            // Reset spawn tracking for new scene
            if (SpawnManager.Instance != null) {
                SpawnManager.Instance.OnSceneLoaded();
            }

            // Detect current mode
            DetectCurrentSceneMode();

            // Only reset match if we're actually loading a new scene, not during dynamic spawning
            if (mode == LoadSceneMode.Single) {
                ResetMatch();
            }
        }
        private void DetectCurrentSceneMode() {

            if (SifaGameMode.Instance != null) {
                currentGameMode = SifaGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Sifa;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (KnifeGameMode.Instance != null) {
                currentGameMode = KnifeGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Knife;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                    UpdateUIForGameMode();
                }

                return;
            }
            else if (PryatkiGameMode.Instance != null) {
                currentGameMode = PryatkiGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Pryatki;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (AssykGameMode.Instance != null) {
                currentGameMode = AssykGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Assyk;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (KlassikiGameMode.Instance != null) {
                currentGameMode = KlassikiGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Klassiki;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (ZhmurkiGameMode.Instance != null) {
                currentGameMode = ZhmurkiGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Zhmurki;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;

            }
            else if (CapsGameMode.Instance != null) {
                currentGameMode = CapsGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Caps;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (AltPryatkiGameMode.Instance != null) {
                currentGameMode = AltPryatkiGameMode.Instance;
                selectedGameMode = ESelectedGameMode.AltPryatki;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (DodgeBallGameMode.Instance != null) {
                currentGameMode = DodgeBallGameMode.Instance;
                selectedGameMode = ESelectedGameMode.DodgeBall;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (BottleGameMode.Instance != null) {
                currentGameMode = BottleGameMode.Instance;
                selectedGameMode = ESelectedGameMode.Bottle;
                if (Object.HasStateAuthority && currentGameMode != null) {
                    State = EGameplayState.PreMatch;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, currentGameMode.LobbyDuration);
                }
                UpdateUIForGameMode();
                return;
            }
            else if (SceneManager.GetActiveScene().name == VictorySceneName) {
                currentGameMode = null;
                selectedGameMode = ESelectedGameMode.VictoryScene;

                if (Object.HasStateAuthority) {
                    State = EGameplayState.GameOver;
                    RemainingTime = TickTimer.CreateFromSeconds(Runner, nextMatchPrepareDuration);
                }
                return;
            }
            else if (SceneManager.GetActiveScene().name == LobbyAfterGameSceneName || SceneManager.GetActiveScene().name == LobbySceneName) {
                currentGameMode = null;
                selectedGameMode = ESelectedGameMode.Lobby;

                if (Object.HasStateAuthority) {
                    // Don't reset state if ready timer is already started (players are ready)
                    if (!isReadyTimerStarted) {
                        State = EGameplayState.Lobby;
                        RemainingTime = TickTimer.None;
                    }
                }
                return;
            }
        }

        private void UpdateUIForGameMode() {
            // Найти UIGameplayInfo и обновить tutorial изображения и описание
            var gameplayInfo = FindObjectOfType<UIGameplayInfo>();
            if (gameplayInfo != null && currentGameMode != null) {
                gameplayInfo.LoadTutorialImages(selectedGameMode);
                gameplayInfo.SetGameModeDescription(currentGameMode.ModeDescription);
            }
        }

        private void CheckToFinishGame() {
            if (!Object.HasStateAuthority) {
                return;
            }

            int winnerCount = 0;
            string winnerNickname = "";
            PlayerRef winnerRef = PlayerRef.None;

            // Count winners - by default all winners
            foreach (var kv in PlayerData) {
                if (kv.Value.IsAlive) {
                    winnerCount++;
                    winnerNickname = kv.Value.Nickname;
                    winnerRef = kv.Key;
                }
            }

            //If everyone lost then its draw
            if (IsEveryoneLost()) {
                lastWinner = "No one";

                State = EGameplayState.WinnerDeclare;
                RemainingTime = TickTimer.CreateFromSeconds(Runner, nextMatchPrepareDuration);

                return;
            }

            // If there is a single winner
            else if (winnerCount == 1) {
                // Save winner nickname
                lastWinner = winnerNickname;

                // Award money to winner
                if (PlayerData.TryGet(winnerRef, out var winnerData)) {
                    // winnerData.Money += 1000;
                    PlayerData.Set(winnerRef, winnerData);
                }

                State = EGameplayState.WinnerDeclare;
                RemainingTime = TickTimer.CreateFromSeconds(Runner, nextMatchPrepareDuration);

                return;
            }
            else if (Object.HasStateAuthority) {
                State = EGameplayState.EndMatch;
                RemainingTime = TickTimer.CreateFromSeconds(Runner, nextMatchPrepareDuration);
                return;
            }
        }
        private void LoadVictoryScene() {
            if (!Object.HasStateAuthority || State == EGameplayState.Loading) {
                return;
            }

            State = EGameplayState.Loading;

#if UNITY_EDITOR
            SceneManager.LoadScene(VictorySceneName);
#else
            Runner.LoadScene(VictorySceneName);
#endif

        }
        private void LoadLobbyScene() {
            if (!Object.HasStateAuthority || State == EGameplayState.Loading) {
                return;
            }

            State = EGameplayState.Loading;

#if UNITY_EDITOR
            SceneManager.LoadScene(LobbyAfterGameSceneName);
#else
            Runner.LoadScene(LobbyAfterGameSceneName);
#endif

        }
        private void LoadNextScene() {
            if (!Object.HasStateAuthority || State == EGameplayState.Loading) {
                return;
            }

            // Check if we've completed 7 scenes (6 rounds, since we start from scene 2)
            // currentCycleIndex represents completed rounds
            if (currentCycleIndex >= gameAmount) {
                // Game is over after 7 scenes, determine winner by money/points
                DetermineGameWinnerByMoney();
                return;
            }

            State = EGameplayState.Loading;

#if UNITY_EDITOR
            StartCoroutine(LoadNextSceneCoroutine(GetNextSceneIndex()));
#else
            SceneRef newSceneRef = SceneRef.FromIndex(GetNextSceneIndex());
            Runner.LoadScene(newSceneRef);
#endif
        }

        #endregion

        #region Player Management
        // Flag to prevent multiple countdowns
        private bool isReadyTimerStarted = false;

        // This method is called when a player is ready
        public void SetPlayerReady(PlayerRef playerRef) {
            if (!Object.HasStateAuthority) {
                return;
            }
            if (PlayerData.ContainsKey(playerRef)) {
                var pd = PlayerData[playerRef];
                pd.IsReady = true;
                PlayerData.Set(playerRef, pd);
            }
            CheckAllPlayersReady();
        }

        // Check if majority of players are ready to start a Game
        private void CheckAllPlayersReady() {
            // Use state authority to check all players
            if (!Object.HasStateAuthority) {
                return;
            }

            int connectedPlayerCount = 0;
            int readyPlayerCount = 0;

            // Count connected and ready players
            foreach (var kvp in PlayerData) {
                if (kvp.Value.IsConnected) {
                    connectedPlayerCount++;
                    if (kvp.Value.IsReady) {
                        readyPlayerCount++;
                    }
                }
            }

            // Check if majority of players are ready (more than 50%)
            bool majorityReady = connectedPlayerCount > 0 && readyPlayerCount > connectedPlayerCount / 2;

            if (majorityReady && !isReadyTimerStarted) {
                isReadyTimerStarted = true;
                RemainingTime = TickTimer.CreateFromSeconds(Runner, loadNextSceneDuration);
                State = EGameplayState.ReadyToStartGame;
            }
        }

        // Reset the ready timer when player composition changes
        private void ResetReadyTimer() {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Only reset if we're in ready state or lobby
            if (State == EGameplayState.ReadyToStartGame || State == EGameplayState.Lobby) {
                isReadyTimerStarted = false;
                if (State == EGameplayState.ReadyToStartGame) {
                    State = EGameplayState.Lobby;
                }
            }
        }
        #endregion

        #region Reset Methods
        public void ResetMatch() {
            if (!Object.HasStateAuthority) {
                return;
            }

            // Reset player stats
            foreach (var pair in PlayerData) {
                var pd = pair.Value;
                pd.CapsAmount = 0;
                pd.AssykAmount = 0;
                pd.IsReady = false;
                pd.IsAlive = true;
                PlayerData.Set(pair.Key, pd);
            }
            // Reset the game mode state
            currentGameMode?.ResetModeState();
            isReadyTimerStarted = false;
        }
        public void ResetGame() {
            if (!Object.HasStateAuthority) {
                return;
            }
            // Reset player stats
            foreach (var pair in PlayerData) {
                var pd = pair.Value;
                pd.CapsAmount = 0;
                pd.AssykAmount = 0;
                pd.IsAlive = true;
                pd.IsReady = false;
                PlayerData.Set(pair.Key, pd);
            }

            DespawnAllPlayers();

            // Reset scene cycle counter
            currentCycleIndex = 0;
        }
        #endregion


        #region Spawn/Despawn Methods
        private async void SpawnPlayer(PlayerRef playerRef) {
            if (!Object.HasStateAuthority) {
                return;
            }
            // Check if player object already exists or spawn is already in progress.
            if (Runner.GetPlayerObject(playerRef) != null || pendingSpawns.Contains(playerRef)) {
                return;
            }
            // Initialize player data if not present.
            if (!PlayerData.ContainsKey(playerRef)) {
                var newPD = new PlayerData {
                    PlayerRef = playerRef,
                    Nickname = playerRef.ToString(),
                    // Mid-game joiners start as dead only if squid game mode is enabled
                    IsAlive = State == EGameplayState.Lobby || State == EGameplayState.ReadyToStartGame, // Mid-game joiners start as dead
                    IsConnected = true,
                    CurrentHatId = -1, // Нет шапочки по умолчанию
                };
                PlayerData.Add(playerRef, newPD);

                // Reset ready timer when new player joins (only in lobby, and only if timer was already started)
                if ((State == EGameplayState.Lobby || State == EGameplayState.ReadyToStartGame) && isReadyTimerStarted) {
                    //ResetReadyTimer();
                }
            }

            // Восстановить шапочку игрока если она есть
            RestorePlayerHat(playerRef);
            else {
                var pd = PlayerData[playerRef];
                pd.IsConnected = true;
                PlayerData.Set(playerRef, pd);

                // Reset ready timer when player reconnects (only if timer was already started)
                if (isReadyTimerStarted) {
                    //ResetReadyTimer();
                }
            }

            pendingSpawns.Add(playerRef);

            var sp = GetSpawnPoint(playerRef);

            if (sp == null) {
                pendingSpawns.Remove(playerRef);
                return;
            }

            // Spawn the player asynchronously and check if player is still valid after spawn.
            var netObj = await Runner.SpawnAsync(playerController, sp.position, sp.rotation, playerRef);
            if (netObj != null && Runner.IsPlayerValid(playerRef)) {
                Runner.SetPlayerObject(playerRef, netObj);
            }

            pendingSpawns.Remove(playerRef);
        }
        private void DespawnPlayer(PlayerRef playerRef, PlayerController pc) {
            if (!Object.HasStateAuthority) {
                return;
            }
            if (PlayerData.TryGet(playerRef, out var pd)) {
                pd.IsConnected = false;
                // Only set IsAlive = false if squid game mode is enabled
                if (IsSquidGameMode) {
                    pd.IsAlive = false;
                }
                PlayerData.Set(playerRef, pd);
            }
            if (pc != null) {
                Runner.Despawn(pc.Object);
            }

            if (selectedGameMode == ESelectedGameMode.Klassiki && KlassikiGameMode.Instance != null) {
                KlassikiGameMode.Instance.HandlePlayerDisconnected(playerRef);
            }

            // Check if remaining connected players can start the game
            CheckAllPlayersReady();
        }
        private void DespawnAllPlayers() {
            if (!Object.HasStateAuthority) {
                return;
            }
            // Despawn objects for all players
            foreach (var kv in PlayerData) {
                var obj = Runner.GetPlayerObject(kv.Key);
                if (obj != null) {
                    Runner.Despawn(obj);
                }
            }
        }
        private Transform GetSpawnPoint(PlayerRef playerRef) {
            if (SpawnManager.Instance == null) {
                return null;
            }

            // Check if player is dead and needs loser spawn point (regardless of IsSquidGameMode)
            if (PlayerData.TryGet(playerRef, out var pd) && !pd.IsAlive) {
                // Ensure currentGameMode exists and has a LoserSpawnPoint
                if (currentGameMode != null && currentGameMode.LoserSpawnPoint != null) {
                    return currentGameMode.LoserSpawnPoint;
                }
                // Fallback to regular spawn point if no loser spawn point available
                Debug.LogWarning($"[GameManager] No LoserSpawnPoint available for dead player {playerRef}, using regular spawn point");
                // For dead players, use random spawn (not sequential)
                return SpawnManager.Instance.GetRandomSpawnPoint();
            }

            // For alive players, always use sequential spawning initially
            // SpawnManager will handle switching to random after initial phase
            return SpawnManager.Instance.GetSpawnPoint(playerRef, true);
        }
        #endregion


        #region RPC Methods
        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Reliable)]
        private void RPC_PlayerKilled(PlayerRef killer, PlayerRef victim, EWeaponType wtype, bool isCriticalKill) {
            string kNick = "";
            string vNick = "";

            if (PlayerData.TryGet(killer, out var kd)) {
                kNick = kd.Nickname;
            }
            if (PlayerData.TryGet(victim, out var vd)) {
                vNick = vd.Nickname;
            }

            // Ensure gameUI reference exists
            if (gameUI == null) {
                gameUI = UIGame.Instance;
            }

            if (gameUI != null) {
                gameUI.GameplayView.KillFeed.ShowKill(kNick, vNick, wtype, isCriticalKill);
            }
        }
        #endregion


        #region Helper Methods


        public bool IsEveryoneLost() {
            foreach (var kv in PlayerData) {
                if (kv.Value.IsAlive) {
                    return false;
                }
            }
            return true;
        }

        // TBD Change logic here
        public int GetNextSceneIndex() {
            const int startScene = 2;
            const int endScene = 7;
            int cycleLength = endScene - startScene + 1;

            int nextSceneIndex = (currentCycleIndex % cycleLength) + startScene;
            currentCycleIndex++;

            return nextSceneIndex;
        }
        public PlayerRef GetAnyAlivePlayerRef() {
            foreach (var kv in PlayerData) {
                if (kv.Value.IsConnected) {
                    return kv.Key;
                }
            }
            return PlayerRef.None;
        }

        public PlayerRef GetWinningPlayers() {
            foreach (var kv in PlayerData) {
                if (kv.Value.IsAlive) {
                    return kv.Key;
                }
            }
            return PlayerRef.None;
        }

        private void DetermineGameWinnerByMoney() {
            if (!Object.HasStateAuthority) {
                return;
            }

            int maxMoney = -1;
            string winnerNickname = "";
            PlayerRef winnerRef = PlayerRef.None;
            int winnersCount = 0;

            // Find the player(s) with the most money
            foreach (var kv in PlayerData) {
                if (kv.Value.IsConnected) {
                    if (kv.Value.Money > maxMoney) {
                        maxMoney = kv.Value.Money;
                        winnerNickname = kv.Value.Nickname;
                        winnerRef = kv.Key;
                        winnersCount = 1;
                    }
                    else if (kv.Value.Money == maxMoney) {
                        winnersCount++;
                        if (winnersCount == 2) {
                            winnerNickname = "Tie";
                        }
                    }
                }
            }

            // Set the winner
            if (winnersCount == 1) {
                lastWinner = winnerNickname;
            }
            else {
                lastWinner = "No one";
            }

            // Transition to victory scene
            State = EGameplayState.WinnerDeclare;
            RemainingTime = TickTimer.CreateFromSeconds(Runner, 1f);
        }

        // Восстановить шапочку игрока при спавне
        private void RestorePlayerHat(PlayerRef playerRef) {
            if (!Object.HasStateAuthority) return;

            if (PlayerData.TryGet(playerRef, out var playerData) && playerData.CurrentHatId >= 0) {
                // Задержка для того чтобы игрок успел заспавниться
                DOVirtual.DelayedCall(0.5f, () => {
                    if (SellerManager.Instance != null) {
                        SellerManager.Instance.RPC_EquipHat(playerRef, playerData.CurrentHatId);
                    }
                });
            }
        }
        #endregion


        #region Debug and Testing
        //Editor
        private IEnumerator LoadNextSceneCoroutine(int sceneIndex) {
            // On the server, initiate the scene switch
            SceneRef sceneRef = SceneRef.FromIndex(sceneIndex);

            Scene currentScene = SceneManager.GetActiveScene();
            SceneRef currentSceneRef = SceneRef.FromIndex(currentScene.buildIndex);

            // Get or add a NetworkSceneManager for scene loading
            INetworkSceneManager sceneManager = Runner.GetComponent<INetworkSceneManager>();
            if (sceneManager == null) {
                sceneManager = Runner.gameObject.AddComponent<NetworkSceneManagerDefault>();
            }

            // Unload the current scene and wait until the operation is complete
            var unloadOp = sceneManager.UnloadScene(currentSceneRef);
            yield return new WaitUntil(() => unloadOp.IsDone);

            // Load the new scene and wait until the operation is complete
            var loadOp = sceneManager.LoadScene(sceneRef, new NetworkLoadSceneParameters());
            yield return new WaitUntil(() => loadOp.IsDone);

            // Set the new scene as active
            Scene newScene = SceneManager.GetSceneByBuildIndex(sceneIndex);
            if (newScene.IsValid()) {
                SceneManager.SetActiveScene(newScene);
            }
        }
        //TBD
        public void PlayerKilled(PlayerRef killer, PlayerRef victim, EWeaponType weaponType, bool isCriticalKill) {
            if (!Object.HasStateAuthority) return;

            if (PlayerData.TryGet(killer, out var kd)) {
                kd.Kills++;
                kd.LastKillTick = Runner.Tick;
                PlayerData.Set(killer, kd);
            }
            if (PlayerData.TryGet(victim, out var vd)) {
                vd.Deaths++;
                // Only set IsAlive = false if squid game mode is enabled
                if (IsSquidGameMode) {
                    vd.IsAlive = false;
                }
                PlayerData.Set(victim, vd);
            }

            RPC_PlayerKilled(killer, victim, weaponType, isCriticalKill);

            if (State == EGameplayState.Match) {

            }
        }

        /// <summary>
        /// Called when a player disconnects from the game
        /// </summary>
        public void OnPlayerDisconnected(PlayerRef playerRef) {
            if (!Object.HasStateAuthority) return;

            // Mark player for cleanup after delay
            disconnectedPlayers[playerRef] = Time.time;
        }

        /// <summary>
        /// Removes disconnected players from PlayerData after cleanup delay
        /// </summary>
        private void CleanupDisconnectedPlayers() {
            if (!Object.HasStateAuthority) return;

            var playersToRemove = new List<PlayerRef>();
            var currentTime = Time.time;

            foreach (var kvp in disconnectedPlayers) {
                var playerRef = kvp.Key;
                var disconnectTime = kvp.Value;

                // Check if enough time has passed since disconnect
                if (currentTime - disconnectTime >= disconnectedPlayerCleanupDelay) {
                    // Verify player is still disconnected and not reconnected
                    if (PlayerData.TryGet(playerRef, out var pd) && !pd.IsConnected) {
                        // Remove from PlayerData
                        PlayerData.Remove(playerRef);
                        playersToRemove.Add(playerRef);
                    }
                    else {
                        // Player reconnected, remove from cleanup list
                        playersToRemove.Add(playerRef);
                    }
                }
            }

            // Remove processed players from cleanup tracking
            foreach (var playerRef in playersToRemove) {
                disconnectedPlayers.Remove(playerRef);
            }
        }
        #endregion
    }
}
