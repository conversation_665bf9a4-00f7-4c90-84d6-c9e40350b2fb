using UnityEngine;
using Fusion;

namespace SimpleFPS
{
    /// <summary>
    /// Базовый класс для всех шапочек. Наследуется от NetworkBehaviour для сетевой синхронизации.
    /// </summary>
    public class BaseHat : NetworkBehaviour
    {
        [Header("Hat Info")]
        public int hatId; // Уникальный ID шапочки
        public string hatName; // Название шапочки

        [Header("Visual Settings")]
        public GameObject visualModel; // Визуальная модель шапочки

        private HatController hatController;

        public override void Spawned()
        {
            // Получить компонент HatController
            hatController = GetComponent<HatController>();
            if (hatController == null)
            {
                hatController = gameObject.AddComponent<HatController>();
            }

            // Установить ID шапочки в контроллер
            hatController.hatId = hatId;
        }

        // Метод для настройки визуала шапочки (можно переопределить в наследниках)
        public virtual void SetupVisual()
        {
            if (visualModel != null)
            {
                visualModel.SetActive(true);
            }
        }

        // Метод для скрытия визуала шапочки
        public virtual void HideVisual()
        {
            if (visualModel != null)
            {
                visualModel.SetActive(false);
            }
        }
    }
}
