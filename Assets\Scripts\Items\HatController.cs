using UnityEngine;
using Fusion;

namespace SimpleFPS
{
    public class HatController : NetworkBehaviour
    {
        [Header("Hat Settings")]
        public int hatId; // ID шапочки

        [Header("Attachment Settings")]
        public Vector3 headOffset = new Vector3(0, 0.2f, 0); // Смещение относительно головы
        public Vector3 rotationOffset = Vector3.zero; // Поворот относительно головы

        private PlayerController attachedPlayer;
        private Transform headTransform;

        public void AttachToPlayer(PlayerController player)
        {
            attachedPlayer = player;

            // Найти голову игрока (обычно это дочерний объект с именем "Head" или подобным)
            // Если нет специального объекта головы, используем сам transform игрока
            headTransform = FindHeadTransform(player.transform);
            if (headTransform == null)
            {
                headTransform = player.transform;
            }

            // Прикрепить шапочку к голове
            transform.SetParent(headTransform);
            transform.localPosition = headOffset;
            transform.localRotation = Quaternion.Euler(rotationOffset);
        }

        private Transform FindHeadTransform(Transform playerTransform)
        {
            // Попытаться найти голову по имени
            Transform head = playerTransform.Find("Head");
            if (head != null) return head;

            head = playerTransform.Find("head");
            if (head != null) return head;

            // Поиск в дочерних объектах
            foreach (Transform child in playerTransform.GetComponentsInChildren<Transform>())
            {
                if (child.name.ToLower().Contains("head"))
                {
                    return child;
                }
            }

            return null;
        }

        public override void FixedUpdateNetwork()
        {
            // Убедиться, что шапочка остается прикрепленной к игроку
            if (attachedPlayer != null && headTransform != null)
            {
                if (transform.parent != headTransform)
                {
                    transform.SetParent(headTransform);
                    transform.localPosition = headOffset;
                    transform.localRotation = Quaternion.Euler(rotationOffset);
                }
            }
        }

        public void DetachFromPlayer()
        {
            if (attachedPlayer != null)
            {
                attachedPlayer.CurrentHat = null;
                attachedPlayer = null;
            }

            transform.SetParent(null);

            // Уничтожить шапочку
            if (Object != null && Object.IsValid)
            {
                Runner.Despawn(Object);
            }
        }
    }
}
