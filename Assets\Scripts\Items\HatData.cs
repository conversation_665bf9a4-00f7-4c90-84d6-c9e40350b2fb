using UnityEngine;
using Fusion;

namespace SimpleFPS
{
    [System.Serializable]
    public class HatData
    {
        [Toolt<PERSON>("Уникальный ID шапочки")]
        public int hatId;

        [Tooltip("Название шапочки")]
        public string hatName;

        [<PERSON>lt<PERSON>("Префаб шапочки для надевания на голову")]
        public NetworkPrefabRef hatPrefab;

        [Tooltip("Цена шапочки")]
        public int price;

        [Tooltip("Визуальное представление в магазине")]
        public GameObject displayObject;
    }
}
