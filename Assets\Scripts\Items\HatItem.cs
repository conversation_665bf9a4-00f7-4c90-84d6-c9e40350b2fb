using UnityEngine;
using Fusion;
using SimpleFPS;

namespace SimpleFPS
{
    public class HatItem : SellableItem
    {
        [Header("Hat Settings")]
        public int hatId; // Уникальный ID шапочки
        public GameObject hatPrefab; // Префаб шапочки для надевания на голову

        public override void AttachToPlayer(PlayerController player)
        {
            // Шапочки не берутся в руки, поэтому этот метод пустой
        }

        public override void ItemAbility(PlayerController player)
        {
            // Шапочки не имеют способностей, поэтому этот метод пустой
        }

        public override void RequestToUse(PlayerRef playerRef)
        {
            // Проверяем, не купил ли игрок уже эту шапочку
            if (HasPlayerAlreadyBoughtHat(playerRef, hatId))
            {
                return; // Игрок уже купил эту шапочку
            }

            // Используем специальную логику покупки шапочек
            if (SellerManager.Instance != null)
            {
                SellerManager.Instance.RPC_RequestHatPurchase(playerRef, hatId);
            }
        }

        private bool HasPlayerAlreadyBoughtHat(PlayerRef playerRef, int hatId)
        {
            // Проверяем через GameManager, есть ли у игрока уже эта шапочка
            if (GameManager.Instance != null && GameManager.Instance.PlayerData.TryGet(playerRef, out var playerData))
            {
                // Здесь можно добавить логику проверки купленных шапочек
                // Пока что проверяем только текущую шапочку
                return playerData.CurrentHatId == hatId;
            }
            return false;
        }
    }
}
