using System.Collections.Generic;
using Fusion;
using UnityEngine;

namespace SimpleFPS {
    public class SellerManager : NetworkBehaviour {
        public static SellerManager Instance { get; private set; }

        [Header("Seller Settings")]
        [Tooltip("List of items for sale")]
        [SerializeField] private List<SellableItemData> sellableItems = new List<SellableItemData>();

        [Tooltip("List of hats for sale")]
        [SerializeField] private List<HatData> sellableHats = new List<HatData>();

        [Tooltip("Spawn point for purchased items")]
        [SerializeField] private Transform purchaseSpawnPoint;

        // Server-side protection against rapid duplicate purchases
        private readonly Dictionary<PlayerRef, float> lastPurchaseTimes = new();
        private readonly Dictionary<PlayerRef, int> lastPurchaseIndices = new();
        private const float SERVER_PURCHASE_COOLDOWN = 0.3f; // 300ms server-side cooldown

        private void Awake() {
            // Simple singleton implementation
            if (Instance == null) {
                Instance = this;
            }
            else {
                Destroy(gameObject);
            }
        }

        // RPC for purchase requests - checks money, deducts cost, removes display item, spawns actual item
        // Special case: SellableItemIndex 5 stays on shelf after purchase (infinite stock)
        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void RPC_RequestPurchase(PlayerRef playerRef, int sellableItemIndex) {
            // Validate index
            if (sellableItemIndex < 0 || sellableItemIndex >= sellableItems.Count)
                return;

            // Server-side protection: prevent rapid duplicate purchases
            float currentTime = Time.time;
            if (lastPurchaseTimes.TryGetValue(playerRef, out float lastTime) &&
                lastPurchaseIndices.TryGetValue(playerRef, out int lastIndex)) {
                if (currentTime - lastTime < SERVER_PURCHASE_COOLDOWN && lastIndex == sellableItemIndex) {
                    return; // Too soon to purchase the same item again
                }
            }

            SellableItemData itemData = sellableItems[sellableItemIndex];

            // Check if item is still available (not already purchased by someone else)
            if (itemData.displayObject != null && sellableItemIndex != 5) {
                if (itemData.displayObject.TryGetComponent<SellableItem>(out var sellableItem)) {
                    if (!sellableItem.IsAvailable) {
                        return; // Item already purchased
                    }
                }
            }

            // Get player data
            if (!GameManager.Instance.PlayerData.TryGet(playerRef, out PlayerData pd))
                return;

            // Check if player has enough money
            if (pd.Money < itemData.price) {
                return;
            }

            // Update purchase tracking
            lastPurchaseTimes[playerRef] = currentTime;
            lastPurchaseIndices[playerRef] = sellableItemIndex;

            // Deduct cost
            pd.Money -= itemData.price;
            GameManager.Instance.PlayerData.Set(playerRef, pd);

            // Mark display object as unavailable and hide it (except for SellableItemIndex 5)
            if (itemData.displayObject != null && sellableItemIndex != 5) {
                // Mark the sellable item as unavailable on all clients (this will also hide it)
                if (itemData.displayObject.TryGetComponent<SellableItem>(out _)) {
                    RPC_MarkItemUnavailable(sellableItemIndex);
                }
            }

            // Optional: remove item from list to prevent repurchase
            // sellableItems.RemoveAt(sellableItemIndex);

            // Spawn the actual item
            if (purchaseSpawnPoint != null && itemData.itemPrefab.IsValid) {
                Runner.Spawn(itemData.itemPrefab, purchaseSpawnPoint.position, purchaseSpawnPoint.rotation, playerRef);
            }
        }

        // RPC для покупки шапочек
        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void RPC_RequestHatPurchase(PlayerRef playerRef, int hatId) {
            // Найти шапочку по ID
            HatData hatData = sellableHats.Find(hat => hat.hatId == hatId);
            if (hatData == null) {
                return; // Шапочка не найдена
            }

            // Проверить, не купил ли игрок уже эту шапочку
            if (GameManager.Instance.PlayerData.TryGet(playerRef, out var playerData)) {
                if (playerData.CurrentHatId == hatId) {
                    return; // Игрок уже носит эту шапочку
                }
            }
            else {
                return; // Данные игрока не найдены
            }

            // Проверить, хватает ли денег
            if (playerData.Money < hatData.price) {
                return; // Недостаточно денег
            }

            // Списать деньги
            playerData.Money -= hatData.price;

            // Установить новую шапочку
            playerData.CurrentHatId = hatId;

            // Сохранить изменения
            GameManager.Instance.PlayerData.Set(playerRef, playerData);

            // Экипировать шапочку на игрока
            RPC_EquipHat(playerRef, hatId);
        }

        // RPC для экипировки шапочки
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        public void RPC_EquipHat(PlayerRef playerRef, int hatId) {
            // Найти игрока
            if (Runner.TryGetPlayerObject(playerRef, out var playerObj)) {
                if (playerObj.TryGetComponent<PlayerController>(out var playerController)) {
                    EquipHatOnPlayer(playerController, hatId);
                }
            }
        }

        private void EquipHatOnPlayer(PlayerController player, int hatId) {
            // Снять старую шапочку если есть
            if (player.CurrentHat != null) {
                Runner.Despawn(player.CurrentHat);
                player.CurrentHat = null;
            }

            // Найти данные новой шапочки
            HatData hatData = sellableHats.Find(hat => hat.hatId == hatId);
            if (hatData != null && hatData.hatPrefab.IsValid) {
                // Создать новую шапочку на голове игрока
                var hatObject = Runner.Spawn(hatData.hatPrefab, player.transform.position, player.transform.rotation, player.Object.InputAuthority);
                player.CurrentHat = hatObject;

                // Прикрепить шапочку к голове игрока (это нужно будет настроить в префабе шапочки)
                if (hatObject.TryGetComponent<HatController>(out var hatController)) {
                    hatController.AttachToPlayer(player);
                }
            }
        }

        // RPC to mark an item as unavailable on all clients
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_MarkItemUnavailable(int sellableItemIndex) {
            // Validate index
            if (sellableItemIndex < 0 || sellableItemIndex >= sellableItems.Count)
                return;

            // Don't mark SellableItemIndex 5 as unavailable (item stays on shelf)
            if (sellableItemIndex == 5)
                return;

            SellableItemData itemData = sellableItems[sellableItemIndex];
            if (itemData.displayObject != null) {
                if (itemData.displayObject.TryGetComponent<SellableItem>(out var sellableItem)) {
                    sellableItem.MarkAsPurchased();
                }
            }
        }
    }

    // Data class for items that can be sold
    [System.Serializable]
    public class SellableItemData {
        [Tooltip("Display name")]
        public string itemName;

        [Tooltip("Cost in money")]
        public int price;

        [Tooltip("Actual item prefab to spawn")]
        public NetworkPrefabRef itemPrefab;

        [Tooltip("Visual representation in the shop")]
        public GameObject displayObject;
    }

    // Data class for hats that can be sold
    [System.Serializable]
    public class HatData {
        [Tooltip("Уникальный ID шапочки")]
        public int hatId;

        [Tooltip("Название шапочки")]
        public string hatName;

        [Tooltip("Префаб шапочки для надевания на голову")]
        public NetworkPrefabRef hatPrefab;

        [Tooltip("Цена шапочки")]
        public int price;

        [Tooltip("Визуальное представление в магазине")]
        public GameObject displayObject;
    }
}

