# Система Шапочек - Руководство по Использованию

## 🎩 Обзор Системы

Система шапочек позволяет игрокам покупать косметические предметы (шапочки) в магазине, которые автоматически надеваются на голову персонажа. Каждый игрок может купить каждую шапочку только один раз, при покупке новой шапочки старая автоматически заменяется.

## ✅ СИСТЕМА ГОТОВА К ИСПОЛЬЗОВАНИЮ!

## 📁 Структура Файлов

### Основные Компоненты:
- `Assets/Scripts/Items/HatItem.cs` - Компонент для предметов-шапочек в магазине
- `Assets/Scripts/Items/HatController.cs` - Контроллер для управления шапочкой на голове игрока
- `Assets/Scripts/Items/HatData.cs` - Структура данных для шапочек
- `Assets/Scripts/Items/BaseHat.cs` - Базовый класс для всех шапочек

### Модифицированные Файлы:
- `Assets/Scripts/Player/PlayerData.cs` - Добавлено поле `CurrentHatId`
- `Assets/Scripts/Player/PlayerController.cs` - Добавлено поле `CurrentHat`
- `Assets/Scripts/Utils/SellerManager.cs` - Добавлена логика покупки шапочек
- `Assets/Scripts/Gameplay/GameManager.cs` - Добавлено восстановление шапочек при спавне

## 🛠️ Настройка Шапочки

### 1. Создание Префаба Шапочки:

1. Создайте новый GameObject для шапочки
2. Добавьте визуальную модель (3D модель шапочки)
3. Добавьте компоненты:
   - `NetworkObject` (обязательно для сетевой синхронизации)
   - `BaseHat` или `HatController`
   - Настройте `hatId` (уникальный ID)

### 2. Настройка в SellerManager:

```csharp
[SerializeField] private List<HatData> sellableHats = new List<HatData>();
```

В инспекторе добавьте новую шапочку в список `sellableHats`:
- `hatId` - уникальный ID (например: 1, 2, 3...)
- `hatName` - название шапочки
- `hatPrefab` - ссылка на NetworkPrefab шапочки
- `price` - цена в игровой валюте
- `displayObject` - визуальное представление в магазине

### 3. Создание Предмета в Магазине:

1. Создайте GameObject для отображения в магазине
2. Добавьте компонент `HatItem`
3. Настройте:
   - `hatId` - тот же ID что и в HatData
   - `sellableItemIndex` - индекс в списке sellableItems (если используется)

## 🎮 Как Работает Система

### Покупка Шапочки:
1. Игрок взаимодействует с предметом в магазине (`HatItem`)
2. Проверяется, не носит ли игрок уже эту шапочку
3. Проверяется наличие денег у игрока
4. Списываются деньги и устанавливается `CurrentHatId` в PlayerData
5. Вызывается `RPC_EquipHat` для экипировки шапочки на всех клиентах

### Экипировка Шапочки:
1. Снимается старая шапочка (если есть)
2. Создается новая шапочка через `Runner.Spawn`
3. Шапочка прикрепляется к голове игрока через `HatController.AttachToPlayer`

### Сохранение Между Сценами:
- `CurrentHatId` хранится в `PlayerData` (NetworkStruct)
- При спавне игрока в новой сцене вызывается `RestorePlayerHat`
- Шапочка автоматически восстанавливается через 0.5 секунды после спавна

## 🔧 Настройка Позиции Шапочки

В компоненте `HatController` можно настроить:
- `headOffset` - смещение относительно головы (Vector3)
- `rotationOffset` - поворот относительно головы (Vector3)

Система автоматически ищет объект "Head" в иерархии игрока, если не найден - использует корневой transform.

## 📝 Примеры Использования

### Создание Новой Шапочки:

1. **Создайте префаб шапочки:**
   ```
   HatPrefab
   ├── NetworkObject
   ├── HatController (hatId = 1)
   └── VisualModel
       └── MeshRenderer + MeshFilter
   ```

2. **Добавьте в SellerManager:**
   - hatId: 1
   - hatName: "Красная Кепка"
   - hatPrefab: ссылка на префаб
   - price: 100
   - displayObject: объект в магазине

3. **Создайте объект в магазине:**
   ```
   ShopHatDisplay
   ├── HatItem (hatId = 1)
   ├── MeshRenderer (визуал для магазина)
   └── Collider (для взаимодействия)
   ```

## ⚠️ Важные Замечания

1. **Уникальные ID:** Каждая шапочка должна иметь уникальный `hatId`
2. **NetworkObject:** Префабы шапочек должны быть NetworkObject'ами
3. **Сетевая Регистрация:** Префабы должны быть зарегистрированы в NetworkProjectConfig
4. **Позиционирование:** Настройте `headOffset` для правильного позиционирования на голове
5. **Производительность:** Система автоматически удаляет старые шапочки при смене

## 🐛 Отладка

Если шапочка не появляется:
1. Проверьте, что префаб зарегистрирован как NetworkPrefab
2. Убедитесь, что `hatId` совпадает в HatData и HatItem
3. Проверьте, что у игрока достаточно денег
4. Убедитесь, что SellerManager.Instance не null

Если шапочка неправильно позиционирована:
1. Настройте `headOffset` в HatController
2. Проверьте иерархию игрока на наличие объекта "Head"
3. Убедитесь, что визуальная модель правильно ориентирована в префабе
